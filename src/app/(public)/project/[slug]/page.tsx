import ProjectDetailContent from "@/components/modules/Projects/ProjectDetailContent";
import { ProjectDetailHeader } from "@/components/modules/Projects/ProjectDetailHeader";
import { Button } from "@/components/ui/button";
import { SingleProjectResponse } from "@/types";
import { ChevronLeft } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";

interface ProjectDetailPageProps {
  params: Promise<{ slug: string }>;
}

export const metadata: Metadata = {
  title: "Project Details",
  description:
    "Explore detailed information about this project including technologies used, features, and implementation details.",
};

const ProjectDetailPage = async ({ params }: ProjectDetailPageProps) => {
  const { slug } = await params;

  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_API}/project/${slug}`,
      {
        cache: "no-store",
      }
    );

    if (!res.ok) {
      notFound();
    }

    const { data: project } = (await res.json()) as SingleProjectResponse;

    if (!project) {
      notFound();
    }

    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          {/* Back Button */}
          <Link href="/project">
            <Button variant="ghost" className="mb-8 -ml-4">
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
          </Link>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Project Content */}
            <article className="lg:col-span-12">
              <ProjectDetailHeader project={project} />
              <div className="mt-8">
                <ProjectDetailContent project={project} />
              </div>
            </article>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching project:", error);
    notFound();
  }
};

export default ProjectDetailPage;
