"use client";

import { cn } from "@/lib/utils";

interface HtmlRendererProps {
  content: string;
  className?: string;
}

export default function HtmlRenderer({
  content,
  className,
}: HtmlRendererProps) {
  if (!content) {
    return null;
  }

  return (
    <div
      className={cn(
        "prose prose-gray dark:prose-invert max-w-none",
        "prose-headings:scroll-m-20 prose-headings:font-semibold",
        "prose-h1:text-4xl prose-h1:font-bold prose-h1:tracking-tight",
        "prose-h2:text-3xl prose-h2:font-semibold prose-h2:tracking-tight prose-h2:border-b prose-h2:pb-2",
        "prose-h3:text-2xl prose-h3:font-semibold prose-h3:tracking-tight",
        "prose-h4:text-xl prose-h4:font-semibold prose-h4:tracking-tight",
        "prose-p:leading-7 prose-p:text-muted-foreground",
        "prose-a:text-primary prose-a:underline-offset-4 hover:prose-a:underline",
        "prose-blockquote:border-l-2 prose-blockquote:border-muted-foreground prose-blockquote:pl-6 prose-blockquote:italic",
        "prose-code:relative prose-code:rounded prose-code:bg-muted prose-code:px-[0.3rem] prose-code:py-[0.2rem] prose-code:font-mono prose-code:text-sm",
        "prose-pre:overflow-x-auto prose-pre:rounded-lg prose-pre:bg-muted prose-pre:p-4",
        "prose-ul:my-6 prose-ul:ml-6 prose-ul:list-disc",
        "prose-ol:my-6 prose-ol:ml-6 prose-ol:list-decimal",
        "prose-li:mt-2",
        "prose-table:w-full prose-table:border-collapse prose-table:border prose-table:border-muted",
        "prose-th:border prose-th:border-muted prose-th:px-4 prose-th:py-2 prose-th:text-left prose-th:font-bold prose-th:bg-muted/50",
        "prose-td:border prose-td:border-muted prose-td:px-4 prose-td:py-2",
        "prose-img:rounded-lg prose-img:shadow-md",
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
